import 'package:flutter/material.dart';

class Form<PERSON>ieldLogin extends StatelessWidget {
  final String? hintText;
  TextEditingController controller = TextEditingController();
  TextInputType? keyboardType;
 final Function validator;
  FormFieldLogin({
    super.key,
    this.hintText,
    required this.controller,
    this.keyboardType,
   required this.validator
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: TextFormField(
        keyboardType: keyboardType,
        controller: controller,
        validator:   validator(),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(fontSize: 20, color: Color(0xff24365e)),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(color: Color(0xff24365e), width: 2),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(color: Color(0xff24365e), width: 2),
          ),
        ),
      ),
    );
  }
}
