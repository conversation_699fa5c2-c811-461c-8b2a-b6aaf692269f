

import 'package:flutter/material.dart';

class FormFieldLogin extends StatelessWidget {
  final String? hintText;
   const FormFieldLogin({super.key, this.hintText});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20,vertical: 20),
      child: TextFormField(

        decoration: InputDecoration(

          hintText: hintText,
          hintStyle: TextStyle(fontSize: 20, color: Color(0xff24365e)),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(color: Color(0xff24365e), width: 2),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(color: Color(0xff24365e), width: 2),

          ),
        ),
      ),
    );
  }
}
