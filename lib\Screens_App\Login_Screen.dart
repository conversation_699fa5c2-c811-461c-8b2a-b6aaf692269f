import 'package:flutter/material.dart';
import 'package:tulipperfumes/Screens_App/Home_Screen.dart';
import 'package:tulipperfumes/widgets_auth/ElevatedButton_Login_widget.dart';

import '../widgets_auth/Form_Field_Login_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff3f3f3),
      appBar: AppBar(title: Text("Login Screen"), centerTitle: true),
      body: Column(

        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FormFieldLogin(hintText: "Enter full name"),

          FormFieldLogin(hintText: "Enter full age"),

          FormFieldLogin(hintText: "Enter full number phone"),
          SizedBox(height: 115,),

          ElevatedButtonLoginWidget(onPressed: (){
            Navigator.of(context).push(MaterialPageRoute(builder: (context){return HomeScreen();}));
          })

        ],
      ),
    );
  }
}
