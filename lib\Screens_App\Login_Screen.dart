import 'package:flutter/material.dart';
import 'package:tulipperfumes/Screens_App/Home_Screen.dart';
import 'package:tulipperfumes/widgets_auth/ElevatedButton_Login_widget.dart';

import '../widgets_auth/Form_Field_Login_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  TextEditingController controllerName = TextEditingController();
  TextEditingController controllerAge = TextEditingController();
  TextEditingController controllerNumberPhone = TextEditingController();

  // GlobalKey<FormState> formStateName = GlobalKey<FormState>();
  // GlobalKey<FormState> formStateAge = GlobalKey<FormState>();
  // GlobalKey<FormState> formStateNumberPhone = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff3f3f3),
      appBar: AppBar(title: Text("Login Screen"), centerTitle: true),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FormFieldLogin(
            hintText: "Enter full name",
            controller: controllerName,
            // key: formStateName,
            keyboardType: TextInputType.text,
            validator: (text){
              return null;
            },
          ),
          FormFieldLogin(
            hintText: "Enter full age",
            controller: controllerAge,
            // key: formStateAge,
            keyboardType:TextInputType.number ,
            validator: (text){
              return null;
            },
          ),
          FormFieldLogin(
            hintText: "Enter full number phone",
            controller: controllerNumberPhone,
            // key: formStateNumberPhone,
            keyboardType: TextInputType.phone,
            validator: (text){
              return null;
            },
          ),

          SizedBox(height: 115),

          ElevatedButtonLoginWidget(
            text: "LOGIN",
            onPressed: () {
              Navigator.of(context).pushNamed("homeScreen");
            },
          ),
        ],
      ),
    );
  }
}
