


import 'package:flutter/material.dart';
import 'dart:async';

import 'package:tulipperfumes/Login_Screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {

  @override
  void initState() {
    super.initState();
     var timer = Timer(const Duration(seconds: 5), () {
       Navigator.of(context).push(MaterialPageRoute(builder: (context){
         return LoginScreen();
       }));
  });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: AppBar(
      title: Text("Splash Screen"),
    centerTitle: true,
    ),
      body: Column(
        children: [
         Container(
           decoration: BoxDecoration(
             image: DecorationImage(image: AssetImage('images/image_splash.jpeg')),
           ),
         )
          
        ],
      ),

    );
  }
}
