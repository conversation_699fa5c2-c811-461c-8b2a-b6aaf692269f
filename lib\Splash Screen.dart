import 'package:flutter/material.dart';
import 'dart:async';

import 'package:tulipperfumes/Login_Screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    var timer = Timer(const Duration(seconds: 5), () {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) {
            return LoginScreen();
          },
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 350,
                height: 350,
                margin: EdgeInsets.only(bottom: 45),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('images/image_splash.jpeg'),
                    fit: BoxFit.cover, // لجعل الصورة تملأ الشاشة بالكامل
                  ),
                ),
              ),
              Text(
                "welcome to App",
                style: TextStyle(fontSize: 35, color: Color(0xff24365e),fontWeight:FontWeight.bold ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
